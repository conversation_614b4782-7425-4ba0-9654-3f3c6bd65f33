﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using SmartLibrary.VipDiscovery.Service.Dtos;
using SmartLibrary.VipDiscovery.Service.Enums;
using SmartLibrary.VipDiscovery.Service.Extensions;
using SmartLibrary.VipDiscovery.Service.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.Service.Implement
{
    public class ServiceBase
    {

        /// <summary>
        /// 缓存服务
        /// </summary>
        protected IDistributedCache _distributedCache { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public ServiceBase()
        {
        }
        /// <summary>
        /// 缓存服务
        /// </summary>
        protected IMemoryCache MemoryCache { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public ServiceBase(IDistributedCache distributedCache)
        {
            _distributedCache = distributedCache;
        }

        /// <summary>
        /// 返回正确结果
        /// </summary>
        /// <typeparam name="T">结果类型</typeparam>
        /// <param name="data">结果数据</param>
        /// <param name="total">总条数</param>
        /// <param name="msg">消息</param>
        /// <returns></returns>
        public static Result<T> ReturnSuccesResult<T>(T data, int total = 0, string msg = "OK")
        {
            return ResultHelper.ReturnSuccesResult(data, total, msg);
        }

        /// <summary>
        /// 返回错误结果
        /// </summary>
        /// <param name="msg">错误信息</param>
        /// <returns></returns>
        public static Result<bool> ReturnErrorResult(string msg)
        {
            return ResultHelper.ReturnErrorResult(msg);
        }

        /// <summary>
        /// 返回结果
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <param name="resultCode">状态码</param>
        /// <param name="t">对象</param>
        /// <param name="msg">消息</param>
        /// <param name="total">总数</param>
        /// <returns></returns>
        public static Result<T> ReturnResult<T>(ResultCodeEnum resultCode, T t, string msg = "", int total = 0)
        {
            return ResultHelper.ReturnResult(resultCode, t, msg, total);
        }

        /// <summary>
        /// 返回执行结果
        /// </summary>
        /// <param name="success">是否成功</param>
        /// <param name="msg">消息</param>
        /// <returns></returns>
        public ExecuteResultDTO ReturnExecuteResult(bool success, string objectId, string objectName, string content = "", string msg = "")
        {
            msg = msg.IsEmptyOrWhiteSpace() ? (success ? "操作成功" : "操作失败") : msg;
            return new ExecuteResultDTO() { Success = success, Message = msg, ObjectId = objectId, ObjectName = objectName, Content = content };
        }

        /// <summary>
        /// 返回执行结果
        /// </summary>
        /// <param name="success">是否成功</param>
        /// <param name="msg">消息</param>
        /// <returns></returns>
        public ExecuteResultDTO ReturnExecuteResult(bool success, string msg = "")
        {
            msg = msg.IsEmptyOrWhiteSpace() ? (success ? "操作成功" : "操作失败") : msg;
            return new ExecuteResultDTO() { Success = success, Message = msg };
        }

        /// <summary>
        /// 验证用户名
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns></returns>
        public ExecuteResultDTO ValidateUsername(string username)
        {
            username = username.Trim();

            if (username.IsEmptyOrWhiteSpace())
            {
                return ReturnExecuteResult(false, "用户名不能为空");
            }

            if (username.Length < 4)
            {
                return ReturnExecuteResult(false, "用户名不能少于4位");
            }

            if (username.Length > 50)
            {
                return ReturnExecuteResult(false, "用户名不能超过50位");
            }

            return ReturnExecuteResult(true);
        }

        /// <summary>
        /// 验证密码
        /// </summary>
        /// <param name="password">密码</param>
        /// <returns></returns>
        public ExecuteResultDTO ValidatePassword(string password)
        {
            password = password.Trim();

            if (password.IsEmptyOrWhiteSpace())
            {
                return ReturnExecuteResult(false, "密码不能为空");
            }

            if (password.Length < 8)
            {
                return ReturnExecuteResult(false, "密码不能少于8位");
            }

            if (password.Length > 50)
            {
                return ReturnExecuteResult(false, "密码不能超过50位");
            }

            if (!HasDigit(password))
            {
                return ReturnExecuteResult(false, "密码中必须包含至少一位数字");
            }

            if (!HasLetter(password))
            {
                return ReturnExecuteResult(false, "密码中必须包含至少一位英文字母");
            }

            if (!IsPasswordContainSpecialChars(password))
            {
                return ReturnExecuteResult(false, "密码中必须包含至少一位符号");
            }

            return ReturnExecuteResult(true);
        }

        /// <summary>
        /// 判断字符串中是否包含特殊字符
        /// </summary>
        /// <param name="text">密码</param>
        /// <returns></returns>
        private bool IsPasswordContainSpecialChars(string text)
        {
            var chars = new[] { "~", "!", "#", "@", "$", "%", "^", "&", "*", ".", ",", ";", "-", "+", "|" };

            for (int i = 0; i < chars.Length; i++)
            {
                if (text.Contains(chars[i]))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 判断字符串中是否包含字母
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public static bool HasLetter(string text)
        {
            foreach (char tempchar in text.ToCharArray())
            {
                if (char.IsLetter(tempchar))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 判断字符串中是否数字
        /// </summary>
        /// <param name="text"></param>
        /// <returns></returns>
        public static bool HasDigit(string text)
        {
            foreach (char tempchar in text.ToCharArray())
            {
                if (char.IsDigit(tempchar))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 获取时间区间
        /// </summary>
        /// <param name="timeRange">时间区间</param>
        /// <returns></returns>
        public TimeRangeDTO GetTimeRange(List<string> timeRange)
        {
            var fromDate = DateTime.Now.AddDays(-7);
            var stopDate = DateTime.Now;

            if (timeRange.IsNotNullOrEmpty())
            {
                if (timeRange.Count == 2)
                {
                    var startDateTime = timeRange[0];
                    var endDateTime = timeRange[1];

                    if (startDateTime.IsNotEmptyOrWhiteSpace())
                    {
                        if (DateTime.TryParse(startDateTime, out DateTime startTime))
                        {
                            fromDate = startTime;

                        }
                    }

                    if (endDateTime.IsNotEmptyOrWhiteSpace())
                    {
                        if (DateTime.TryParse(endDateTime, out DateTime endTime))
                        {
                            stopDate = endTime.AddDays(1).AddSeconds(-1);
                        }
                    }
                }
            }

            return new TimeRangeDTO() { StartTime = fromDate, EndTime = stopDate };
        }

        /// <summary>
        /// 获取匹配的值
        /// </summary>
        /// <param name="list"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        protected int GetMatchCount(List<StatsItemDTO<string>> list, string key)
        {
            if (list.IsNotNullOrEmpty())
            {
                var em = list.FirstOrDefault(y => y.Key == key);
                return em == null ? 0 : em.Count;
            }
            return 0;
        }

        /// <summary>
        /// 获取匹配的值
        /// </summary>
        /// <param name="list"></param>
        /// <param name="key"></param>
        /// <returns></returns>
        protected int GetMatchCount(List<StatsItemDTO<int>> list, int key)
        {
            if (list.IsNotNullOrEmpty())
            {
                var em = list.FirstOrDefault(y => y.Key == key);
                return em == null ? 0 : em.Count;
            }
            return 0;
        }

        /// <summary>
        /// 获取数量
        /// </summary>
        /// <param name="list">列表</param>
        /// <param name="key">日期</param>
        /// <param name="logType">日志类型</param>
        /// <returns></returns>
        protected int GetCount(List<StatsDayDTO> list, string key, int logType)
        {
            if (list.IsNullOrEmpty())
                return 0;

            var ele = list.Find(y => y.Day == key && y.LogType == logType);
            return ele == null ? 0 : ele.Count;
        }
    }
}
