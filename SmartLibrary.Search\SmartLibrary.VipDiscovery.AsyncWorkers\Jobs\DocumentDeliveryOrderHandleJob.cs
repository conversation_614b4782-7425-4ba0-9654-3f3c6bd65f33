using Microsoft.Extensions.Logging;
using SmartLibrary.VipDiscovery.AsyncWorkers.Interface;
using SmartLibrary.VipDiscovery.Service.Interface;
using System;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.AsyncWorkers.Jobs
{
    /// <summary>
    /// 文献传递订单处理任务
    /// </summary>
    public class DocumentDeliveryOrderHandleJob : IDocumentDeliveryOrderHandleJob
    {
        private readonly ILogger<DocumentDeliveryOrderHandleJob> _logger;
        private readonly IDocumentDeliveryService _documentDeliveryService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public DocumentDeliveryOrderHandleJob(ILogger<DocumentDeliveryOrderHandleJob> logger,IDocumentDeliveryService documentDeliveryService)
        {
            _documentDeliveryService = documentDeliveryService;
            _logger = logger;
        }

        /// <summary>
        /// 执行文献传递订单处理任务
        /// </summary>
        public async Task Execute()
        {
            try
            {
                _logger.LogInformation("开始执行文献传递订单处理任务");
                _documentDeliveryService.HandleDeliveryOrder();
                _logger.LogInformation("文献传递订单处理任务执行完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行文献传递订单处理任务时发生错误");
                throw;
            }
        }
    }
}
