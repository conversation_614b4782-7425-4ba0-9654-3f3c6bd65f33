﻿using Microsoft.Extensions.Logging;
using SmartLibrary.VipDiscovery.Service.Interface;
using System;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.AsyncWorkers.Jobs
{
    /// <summary>
    /// 文献传递处理中任务
    /// </summary>
    public class DocumentDeliveryHandleProcessingJob
    {
        private readonly ILogger<DocumentDeliveryHandleProcessingJob> _logger;
        private readonly IDocumentDeliveryService _documentDeliveryService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public DocumentDeliveryHandleProcessingJob(ILogger<DocumentDeliveryHandleProcessingJob> logger,IDocumentDeliveryService documentDeliveryService)
        {
            _documentDeliveryService = documentDeliveryService;
            _logger = logger;
        }

        /// <summary>
        /// 执行文献传递处理中任务
        /// </summary>
        public async Task Execute()
        {
            try
            {
                _logger.LogInformation("开始执行文献传递处理中任务");
                _documentDeliveryService.HandleProcessing();
                _logger.LogInformation("文献传递处理中任务执行完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行文献传递处理中任务时发生错误");
                throw;
            }
        }
    }
}
