﻿using Microsoft.Extensions.Logging;
using SmartLibrary.VipDiscovery.Service.Interface;
using System;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.AsyncWorkers.Jobs
{
    /// <summary>
    /// 文献传递配送任务
    /// </summary>
    public class DocumentDeliveryDeliveryJob
    {
        private readonly ILogger<DocumentDeliveryDeliveryJob> _logger;
        private readonly IDocumentDeliveryService _documentDeliveryService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public DocumentDeliveryDeliveryJob(ILogger<DocumentDeliveryDeliveryJob> logger,IDocumentDeliveryService documentDeliveryService )
        {
            _documentDeliveryService = documentDeliveryService;
            _logger = logger;
        }

        /// <summary>
        /// 执行文献传递配送任务
        /// </summary>
        public async Task Execute()
        {
            try
            {
                _logger.LogInformation("开始执行文献传递配送任务");

                _documentDeliveryService.HandleDelivery();
                _logger.LogInformation("文献传递配送任务执行完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行文献传递配送任务时发生错误");
                throw;
            }
        }
    }
}
