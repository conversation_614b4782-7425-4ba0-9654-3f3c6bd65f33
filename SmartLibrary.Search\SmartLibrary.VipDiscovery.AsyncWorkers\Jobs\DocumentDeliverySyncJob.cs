﻿using Furion.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmartLibrary.VipDiscovery.AsyncWorkers.Interface;
using SmartLibrary.VipDiscovery.Service.Interface;
using System;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.AsyncWorkers.Jobs
{
    /// <summary>
    /// 文献传递同步任务
    /// </summary>
    public class DocumentDeliverySyncJob : IDocumentDeliverySyncJob, IScoped
    {
        private readonly ILogger<DocumentDeliverySyncJob> _logger;
        private readonly IDocumentDeliverySyncDispatchService _documentDeliverySyncDispatchService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public DocumentDeliverySyncJob(ILogger<DocumentDeliverySyncJob> logger,IDocumentDeliverySyncDispatchService documentDeliverySyncDispatchService)
        {
            _documentDeliverySyncDispatchService = documentDeliverySyncDispatchService;
            _logger = logger;
        }

        /// <summary>
        /// 执行文献传递同步任务
        /// </summary>
        public async Task Execute()
        {
            try
            {
                _logger.LogInformation("开始执行文献传递同步任务");
                await _documentDeliverySyncDispatchService.SyncDocumentDeliveryPoolAsync();
                _logger.LogInformation("文献传递同步任务执行完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行文献传递同步任务时发生错误");
                throw;
            }
        }
    }
}
