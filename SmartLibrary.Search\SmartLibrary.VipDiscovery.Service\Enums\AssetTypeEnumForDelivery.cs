﻿using SmartLibrary.VipDiscovery.Service.EnumTools;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.Service.Enums
{
    public enum AssetTypeEnumForDelivery
    {
        /// <summary>
        /// 图书
        /// </summary>
        [EnumAttribute("图书")]
        Books = 1,

        /// <summary>
        /// 期刊
        /// </summary>
        [Enum("期刊")]
        Journal = 2,

        /// <summary>
        /// 期刊文献
        /// </summary>
        [Enum("期刊文章")]
        Literature = 3,

        /// <summary>
        /// 学位论文
        /// </summary>
        [Enum("学位论文")]
        Degree = 4,

        /// <summary>
        /// 标准
        /// </summary>
        [Enum("标准")]
        Standard = 5,

        /// <summary>
        /// 会议
        /// </summary>
        [Enum("会议论文")]
        Meeting = 6,

        /// <summary>
        /// 专利
        /// </summary>
        [Enum("专利")]
        Patent = 7,

        /// <summary>
        /// 法律法规
        /// </summary>
        [Enum("政策法规")]
        Law = 8,

        /// <summary>
        /// 成果
        /// </summary>
        [Enum("成果")]
        Achievement = 9,

        /// <summary>
        /// 多媒体
        /// </summary>
        [Enum("多媒体")]
        MultiMedia = 10,

        /// <summary>
        /// 报纸
        /// </summary>
        [Enum("报纸")]
        Newspaper = 11,

        /// <summary>
        /// 科研报告
        /// </summary>
        [Enum("科技报告")]
        ResearchReport = 12,

        /// <summary>
        /// 产品样本
        /// </summary>
        [Enum("产品样本")]
        ProductSamples = 13,

        /// <summary>
        /// 资讯
        /// </summary>
        [Enum("资讯")]
        Information = 14
    }
}
