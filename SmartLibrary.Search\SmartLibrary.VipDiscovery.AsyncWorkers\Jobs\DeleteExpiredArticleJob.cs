using Furion.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmartLibrary.VipDiscovery.AsyncWorkers.Interface;
using SmartLibrary.VipDiscovery.Service.Interface;
using System;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.AsyncWorkers.Jobs
{
    /// <summary>
    /// 删除过期文章任务
    /// </summary>
    public class DeleteExpiredArticleJob : IDeleteExpiredArticleJob, IScoped
    {
        private readonly ILogger<DeleteExpiredArticleJob> _logger;
        private readonly IBookListService _bookListService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="bookListService">专辑服务</param>
        public DeleteExpiredArticleJob(
            ILogger<DeleteExpiredArticleJob> logger,
            IBookListService bookListService)
        {
            _logger = logger;
            _bookListService = bookListService;
        }

        /// <summary>
        /// 执行删除过期文章任务
        /// </summary>
        public async Task Execute()
        {
            try
            {
                _logger.LogInformation("开始执行删除过期文章任务");

                // 调用专辑服务删除过期文章
                _bookListService.DeleteExpiredArticlesFromBookList();

                // 确保异步方法有等待
                await Task.CompletedTask;

                _logger.LogInformation("删除过期文章任务执行完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行删除过期文章任务时发生错误");
                throw;
            }
        }
    }
}
