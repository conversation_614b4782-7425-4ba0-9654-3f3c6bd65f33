﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly.Retry;
using SmartLibrary.Search.EntityFramework.Core.Entities.VipDiscovery;
using SmartLibrary.VipDiscovery.Service.Dtos;
using SmartLibrary.VipDiscovery.Service.Enums;
using SmartLibrary.VipDiscovery.Service.Extensions;
using SmartLibrary.VipDiscovery.Service.Interface;
using SmartLibrary.VipDiscovery.Service.Models;
using SmartLibrary.VipDiscovery.Service.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.Service.Implement
{
    public class DeliveryOrderService : ServiceBase, IDeliveryOrderService
    {
        private const string KrsDeliverOrderDayHanldeTotalCache = nameof(KrsDeliverOrderDayHanldeTotalCache);
        private readonly ILogger<DeliveryOrderService> _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        private readonly IDistributedCache _distributedCache;//使用分布式缓存替换内存缓存
        public DeliveryOrderService(IMemoryCache memoryCache, ILogger<DeliveryOrderService> logger, IHttpClientFactory httpClientFactory, IDistributedCache distributedCache, IConfiguration configuration)
        {
            _logger = logger;
            _httpClientFactory = httpClientFactory;
            _distributedCache = distributedCache;
            MemoryCache = memoryCache;
            _configuration = configuration;
        }

        /// <summary>
        /// 创建订单
        /// </summary>
        /// <param name="titleInfo">文章对象</param>
        /// <returns></returns>
        public DeliveryOrderCreateResultDTO CreateOrder(ZTTitleInfo titleInfo)
        {
            DeliveryOrderCreateResultDTO result = new DeliveryOrderCreateResultDTO();

            if (titleInfo == null || titleInfo.Id.IsEmptyOrWhiteSpace())
            {
                return result;
            }

            var accessToken = GetAccessToken();
            if (accessToken.IsNotEmptyOrWhiteSpace())
            {
                var header = new Dictionary<string, string>();
                header.Add("Authorization", string.Format("bearer {0}", accessToken));

                var deliveryOrderDTO = GetDeliveryOrder(titleInfo);
                var dto = new DeliveryOrderCreateDTO()
                {
                    Account = Common.AppSetting.DeliveryOrderUserName,
                    Password = Common.AppSetting.DeliveryOrderPassword,
                    T_Delivery = deliveryOrderDTO

                };
                var data = JsonConvert.SerializeObject(dto);
                var deliveryData = JsonConvert.SerializeObject(deliveryOrderDTO);
                var res = HttpHelper.HttpPost2(Common.AppSetting.DeliveryOrderAddOrderUrl, deliveryData, header, "application/x-www-form-urlencoded");
                if (res != null && res.IsNotEmptyOrWhiteSpace())
                {
                    try
                    {

                        result = JsonConvert.DeserializeObject<DeliveryOrderCreateResultDTO>(res);
                        return result;
                    }
                    catch
                    {
                    }
                }
            }
            return result;
        }

        /// <summary>
        /// 根据订单号获取订单结果
        /// </summary>
        /// <param name="orderNo"></param>
        /// <returns></returns>
        public async Task<DeliveryOrderGetResultDTO> GetOrderAsync(string orderNo)
        {
            using (var httpClient = this._httpClientFactory.CreateClient(ConstStrings.CreateOrderHttpClientName))
            {
                httpClient.DefaultRequestHeaders.UserAgent.TryParseAdd(ConstStrings.DefaultUserAgent);
                httpClient.Timeout = TimeSpan.FromMinutes(1);
                var accessToken = await GetAccessTokenAsync(httpClient); if (string.IsNullOrWhiteSpace(accessToken)) return new DeliveryOrderGetResultDTO();
                httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", $"bearer {accessToken}");
                var url = $"{Common.AppSetting.DeliveryOrderGetOrderUrl}?orderNo={orderNo}";
                var response = await httpClient.GetAsync(url);
                if (!response.IsSuccessStatusCode) return new DeliveryOrderGetResultDTO();
                var json = await response.Content.ReadAsStringAsync();
                if (string.IsNullOrWhiteSpace(json)) return new DeliveryOrderGetResultDTO();
                _logger.LogDebug($"获取订单结果：【{json}】");
                var result = JsonConvert.DeserializeObject<DeliveryOrderGetResultDTO>(json);
                if (!string.IsNullOrWhiteSpace(result?.Data?.ResultLink)) result.Data.ResultLink = $"{Common.AppSetting.DeliveryOrderPDFHost.Trim('/')}/{result.Data.ResultLink.TrimStart('/')}";
                return result;
            }
        }
        /// <summary>
        /// 获取订单结果
        /// </summary>
        /// <param name="titleInfo">获取订单结果</param>
        /// <returns></returns>
        public DeliveryOrderGetResultDTO GetOrder(string orderNo)
        {
            DeliveryOrderGetResultDTO result = new DeliveryOrderGetResultDTO();

            var accessToken = GetAccessToken();
            _logger.LogDebug($"获取到的token:【{accessToken}】");

            if (accessToken.IsNotEmptyOrWhiteSpace())
            {
                var header = new Dictionary<string, string>();
                header.Add("Authorization", string.Format("bearer {0}", accessToken));

                var url = $"{Common.AppSetting.DeliveryOrderGetOrderUrl}?orderNo={orderNo}";
                var res = HttpHelper.HttpGet(url, header);
                if (!string.IsNullOrWhiteSpace(res))
                {
                    _logger.LogDebug($"获取订单结果：【{res}】");
                    try
                    {
                        result = JsonConvert.DeserializeObject<DeliveryOrderGetResultDTO>(res);

                        if (result != null && result.Data != null && result.Data.ResultLink.IsNotEmptyOrWhiteSpace())
                        {
                            result.Data.ResultLink = $"{Common.AppSetting.DeliveryOrderPDFHost.Trim('/')}/{result.Data.ResultLink.TrimStart('/')}";
                        }
                        return result;
                    }
                    catch
                    {
                    }
                }
            }
            return result;

        }



        /// <summary>
        /// 获取传递订单
        /// </summary>
        /// <param name="titleInfo"></param>
        /// <returns></returns>
        private DeliveryOrderDTO GetDeliveryOrder(ZTTitleInfo titleInfo)
        {
            DeliveryOrderDTO result = new DeliveryOrderDTO();
            if (titleInfo == null || titleInfo.Id.IsEmptyOrWhiteSpace())
            {
                return result;
            }

            var titleType = (AssetTypeEnumForDelivery)Int32.Parse(titleInfo.Type);
            var showType = EnumUtils.GetName(titleType);

            result.Title = titleInfo.Title;
            result.Author = titleInfo.Author;
            result.ShowType = showType;
            result.ZtId = titleInfo.Id;
            result.ProviderUrl =
                titleInfo.ProviderUrl?.Split(";", StringSplitOptions.RemoveEmptyEntries).FirstOrDefault();
            switch (titleType)
            {
                case AssetTypeEnumForDelivery.Literature:

                    var showSourceIssueInfos = new[]
                    {
                        KeyValuePair.Create("年",titleInfo.Date),
                        KeyValuePair.Create("卷",titleInfo.Volume),
                        KeyValuePair.Create("期",titleInfo.Issue),
                    };

                    result.ShowSourceIssue = string.Join(String.Empty,
                        showSourceIssueInfos.Where(x => !string.IsNullOrWhiteSpace(x.Value))
                            .Select(x => string.Concat(x.Value, x.Key)));
                    result.Subject = titleInfo.Subject;
                    result.Description = titleInfo.Date;
                    break;
                case AssetTypeEnumForDelivery.Meeting:
                case AssetTypeEnumForDelivery.ResearchReport:
                case AssetTypeEnumForDelivery.ProductSamples:
                    result.ShowSourceIssue = titleInfo.Date;
                    result.Subject = titleInfo.Subject;
                    result.Description = titleInfo.Date;
                    break;
                case AssetTypeEnumForDelivery.Standard:
                    result.ShowSourceIssue = titleInfo.Standard;
                    result.Subject = titleInfo.Subject;
                    result.Description = titleInfo.Release;
                    break;
                case AssetTypeEnumForDelivery.Achievement:
                    result.ShowSourceIssue = titleInfo.Date;
                    result.Subject = titleInfo.Subject;
                    result.Description = titleInfo.DateCreated;
                    break;
                case AssetTypeEnumForDelivery.Law:
                    result.ShowSourceIssue = titleInfo.Date;
                    result.Subject = titleInfo.Subject;
                    result.Description = titleInfo.DateCreated;
                    break;
                case AssetTypeEnumForDelivery.Patent:
                    result.ShowSourceIssue = titleInfo.Date;
                    result.Subject = titleInfo.StandardType;
                    result.Description = titleInfo.DateCreated;
                    break;
            }
            return ToBase64Encoded(result);
        }

        /// <summary>
        /// 按接口最新v2要求，订单需要对 Title\Author\Subject\Description\ProviderUrl字段进行UrlEncode编码
        /// </summary>
        /// <param name="order"></param>
        /// <returns></returns>
        private DeliveryOrderDTO ToBase64Encoded(DeliveryOrderDTO order)
        {
            order.Title = Base64Encode(order.Title ?? "");
            order.Author = Base64Encode(order.Author ?? "");
            order.Subject = Base64Encode(order.Subject ?? "");
            order.Description = Base64Encode(order.Description ?? "");
            order.ProviderUrl = Base64Encode(order.ProviderUrl ?? "");
            return order;
        }

        ///<summary>
        /// Base64加密，采用utf8编码方式加密
        ///</summary>
        ///<paramname="source">待加密的明文</param>
        ///<returns>加密后的字符串</returns>
        private string Base64Encode(string source)
        {
            return Base64Encode(Encoding.UTF8, source);
        }

        ///<summary>
        /// Base64加密
        ///</summary>
        ///<paramname="encodeType">加密采用的编码方式</param>
        ///<paramname="source">待加密的明文</param>
        ///<returns></returns>
        private string Base64Encode(Encoding encodeType, string source)
        {
            string encode = string.Empty;
            byte[] bytes = encodeType.GetBytes(source);
            try
            {
                encode = Convert.ToBase64String(bytes);
            }
            catch
            {
                encode = source;
            }
            return encode;
        }


        /// <summary>
        /// 获取AccessToken
        /// </summary>
        /// <returns></returns>
        private string GetAccessToken()
        {
            if (MemoryCache.TryGetValue(CacheKey.DeliveryOrderTokenCacheKey, out string token))
            {
                return token;
            }

            var data = JsonConvert.SerializeObject(new DeliveryOrderServiceAuthDTO());

            var res = HttpHelper.HttpPost2(Common.AppSetting.DeliveryOrderAuthTokenUrl, data, null, "application/x-www-form-urlencoded");

            if (res != null && res.IsNotEmptyOrWhiteSpace())
            {
                try
                {
                    var result = JsonConvert.DeserializeObject<DeliveryOrderServiceAuthResultDTO>(res);
                    token = result.access_token;
                }
                catch
                {

                }
            }

            // 缓存配置
            var cacheOptions = new MemoryCacheEntryOptions()
                .SetAbsoluteExpiration(TimeSpan.FromMinutes(30));

            // 刷新缓存
            MemoryCache.Set(CacheKey.DeliveryOrderTokenCacheKey, token, cacheOptions);
            return token;
        }


        /// <summary>
        /// 创建订单
        /// </summary>
        /// <param name="titleInfo"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<DeliveryOrderCreateResultDTO> CreateOrderAsync(ZTTitleInfo titleInfo)
        {
            if (string.IsNullOrWhiteSpace(titleInfo?.Id)) throw new ArgumentNullException(nameof(titleInfo));

            var deliveryOrder = GetDeliveryOrder(titleInfo);
            if (deliveryOrder == null)
            {
                return new DeliveryOrderCreateResultDTO() { Success = "false" };
            }
            var key = $"{KrsDeliverOrderDayHanldeTotalCache}_{DateTime.Now:yyyyMMdd}";
            string cachedata = "1";
            try
            {
                var cache = await _distributedCache.GetAsync(key);
                if (cache?.Length > 0)
                {
                    cachedata = Encoding.UTF8.GetString(cache);
                    if (int.TryParse(cachedata, out var todayTotal) && todayTotal >= 500)
                    {
                        //超出每日限额500条
                        _logger.LogError("订单超出每日限额500条");
                        return null;
                    }
                    cachedata = (todayTotal + 1).ToString();
                }
            }
            catch
            {
            }
            using (var httpClient = this._httpClientFactory.CreateClient(ConstStrings.CreateOrderHttpClientName))
            {
                httpClient.DefaultRequestHeaders.UserAgent.TryParseAdd(ConstStrings.DefaultUserAgent);
                var accessToken = await GetAccessTokenAsync(httpClient);
                if (string.IsNullOrWhiteSpace(accessToken)) return new DeliveryOrderCreateResultDTO();

                httpClient.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", $"bearer {accessToken}");
                var postContent = new FormUrlEncodedContent(GenerateNameValueCollectionsFromObject(deliveryOrder));
                postContent.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");
                var temp = await httpClient.PostAsync(AppSetting.DeliveryOrderAddOrderUrl, postContent);
                temp.EnsureSuccessStatusCode();
                var json = await temp.Content.ReadAsStringAsync();
                this._logger.LogWarning($"创建订单请求：{deliveryOrder.ArticleId},{titleInfo.Title}，返回结果：【{json}】");
                var data = JsonConvert.DeserializeObject<DeliveryOrderCreateResultDTO>(json);
                if ("true".Equals(data?.Success, StringComparison.OrdinalIgnoreCase))
                {
                    //设置限额缓存
                    try
                    {
                        await _distributedCache.SetAsync(key, Encoding.UTF8.GetBytes(cachedata), new DistributedCacheEntryOptions { AbsoluteExpiration = DateTime.Today.AddDays(1) });
                    }
                    catch { }
                }
                return JsonConvert.DeserializeObject<DeliveryOrderCreateResultDTO>(json);
            }
        }

        /// <summary>
        /// 获取tokend
        /// </summary>
        /// <param name="httpClient"></param>
        /// <returns></returns>
        private async Task<string> GetAccessTokenAsync(HttpClient httpClient)
        {
            var token = await this._distributedCache.GetStringAsync(CacheKey.DeliveryOrderTokenCacheKey);

            if (!string.IsNullOrWhiteSpace(token)) return token;
            var orderAuthDto = new DeliveryOrderServiceAuthDTO
            {
                username = AppSetting.DeliveryOrderUserName,
                Password = AppSetting.DeliveryOrderPassword
            };
            var postContent = new FormUrlEncodedContent(GenerateNameValueCollectionsFromObject(orderAuthDto));
            postContent.Headers.ContentType = new MediaTypeHeaderValue("application/x-www-form-urlencoded");
            var temp = await httpClient.PostAsync(AppSetting.DeliveryOrderAuthTokenUrl, postContent);
            temp.EnsureSuccessStatusCode();
            var json = await temp.Content.ReadAsStringAsync();
            var tokenDto = JsonConvert.DeserializeObject<DeliveryOrderServiceAuthResultDTO>(json);
            if (string.IsNullOrWhiteSpace(tokenDto?.access_token)) return string.Empty;

            _ = this._distributedCache.SetStringAsync(CacheKey.DeliveryOrderTokenCacheKey, tokenDto.access_token,
                 new DistributedCacheEntryOptions
                 {
                     AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30)
                 });

            return tokenDto.access_token;
        }

        private static IEnumerable<KeyValuePair<string, string>> GenerateNameValueCollectionsFromObject(object obj) =>
            obj.GetType().GetProperties().Select(x => KeyValuePair.Create(x.Name, x.GetValue(obj)?.ToString()));
    }
}
