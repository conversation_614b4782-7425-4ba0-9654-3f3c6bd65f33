﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.Service.Dtos
{
    public class DeliveryOrderServiceAuthDTO
    {
        [JsonProperty("grant_type")]
        public string grant_type { get; set; } = "password";

        [JsonProperty("username")]
        public string username { get; set; } = "smart_007";

        public string Password { get; set; } = "VipZT_20220505";

    }

    public class DeliveryOrderServiceAuthResultDTO
    {
        /// <summary>
        /// access_token
        /// </summary>
        [JsonProperty("access_token")]
        public string access_token { get; set; }

        /// <summary>
        /// 权限码类型
        /// </summary>
        public string TokenType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ExpiresIn { get; set; }
        public string RefreshToken { get; set; }
        public string UserName { get; set; }
        /// <summary>
        /// 权限是否有效
        /// </summary>
        public string IsAuthenticated { get; set; }
        public string Issued { get; set; }
        public string Expires { get; set; }
    }
}
