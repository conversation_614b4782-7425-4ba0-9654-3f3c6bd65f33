﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.Service.Dtos
{
    /// <summary>
    /// 传递订单创建对象
    /// </summary>
    public class DeliveryOrderCreateDTO
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string Account { get; set; }

        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 传递对象
        /// </summary>
        public DeliveryOrderDTO T_Delivery { get; set; }

    }
    /// <summary>
    /// 传递对象
    /// </summary>
    public class DeliveryOrderDTO
    {
        /// <summary>
        /// 标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 文章类型（期刊文章、学位论文、会议论文、科技报告、产品样本、标准、政策法规、专利）
        /// </summary>
        public string ShowType { get; set; }

        /// <summary>
        /// 期次
        /// </summary>
        public string ShowSourceIssue { get; set; }

        /// <summary>
        /// 作者
        /// </summary>
        public string Author { get; set; }

        /// <summary>
        /// 关键词/主题词
        /// </summary>
        public string Subject { get; set; }

        /// <summary>
        /// 摘要
        /// </summary>
        public string Description { get; set; }

        public string ZtId { get; set; }
        public string ArticleId { get; set; }
        public string ProviderUrl { get; set; }

    }
}
