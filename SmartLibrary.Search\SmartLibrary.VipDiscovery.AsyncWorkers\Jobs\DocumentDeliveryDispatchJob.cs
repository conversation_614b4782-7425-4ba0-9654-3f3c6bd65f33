﻿using Furion.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmartLibrary.VipDiscovery.AsyncWorkers.Interface;
using SmartLibrary.VipDiscovery.Service.Interface;
using System;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.AsyncWorkers.Jobs
{
    /// <summary>
    /// 文献传递调度任务
    /// </summary>
    public class DocumentDeliveryDispatchJob
    {
        private readonly ILogger<DocumentDeliveryDispatchJob> _logger;
        private readonly IDocumentDeliveryService _documentDeliveryService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public DocumentDeliveryDispatchJob(ILogger<DocumentDeliveryDispatchJob> logger,IDocumentDeliveryService documentDeliveryService)
        {
            _documentDeliveryService = documentDeliveryService;
            _logger = logger;
        }

        /// <summary>
        /// 执行文献传递调度任务
        /// </summary>
        public async Task Execute()
        {
            try
            {
                _logger.LogInformation("开始执行文献传递调度任务");

                _documentDeliveryService.Dispatch();
                _logger.LogInformation("文献传递调度任务执行完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行文献传递调度任务时发生错误");
                throw;
            }
        }
    }
}
