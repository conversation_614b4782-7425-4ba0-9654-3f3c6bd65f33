﻿using Autofac.Core;
using Hangfire;
using Hangfire.PostgreSql;
using Microsoft.EntityFrameworkCore;
using Serilog;
using SmartLibrary.Search.EntityFramework.Core.DbContexts;
using SmartLibrary.VipDiscovery.AsyncWorkers;
using SmartLibrary.VipDiscovery.AsyncWorkers.Consumers;
using SmartLibrary.VipDiscovery.AsyncWorkers.Jobs;

using kiwiho.Course.MultipleTenancy.EFcore.Api.Infrastructure;
using Autofac;
using Autofac.Extensions.DependencyInjection;
using SmartLibrary.Search.EsSearchProxy.Core;
using SmartLibrary.Search.EsSearchProxy.Core.ExternalHandler;
using SmartLibrary.VipDiscovery.AsyncWorkers.Interface;


AppContext.SetSwitch("Npgsql.EnableLegacyTimestampBehavior", true);
AppContext.SetSwitch("Npgsql.DisableDateTimeInfinityConversions", true);
var builder = WebApplication.CreateBuilder(args).Inject();
builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory());

// 配置 Serilog
builder.Host.UseSerilog((context, services, configuration) => configuration
    .ReadFrom.Configuration(context.Configuration)
    .ReadFrom.Services(services)
    .Enrich.FromLogContext());

// 添加 Hangfire 服务
builder.Services.AddHangfire(config =>
{
    config.UsePostgreSqlStorage(options =>
    {
        options.UseNpgsqlConnection(builder.Configuration.GetConnectionString("DefaultConnection"));
    });
});
var dbConn = builder.Configuration.GetConnectionString("DefaultConnection");

// 配置 Hangfire 服务器
builder.Services.AddHangfireServer(options =>
{
    options.WorkerCount = builder.Configuration.GetValue<int>("Hangfire:WorkerCount", 5);
});


builder.Services.AddTenantDatabasePerSchema<KrsDbContext>(new ConnectionResolverOption
{
    Key = "default",
    Type = ConnectionResolverType.BySchema,
    ConnectinString = dbConn,
    MigrationAssembly = "SmartLibrary.Search.Database.Migrations",
    DBType = DatabaseIntegration.PostgreSql
});
builder.Services.AddDatabaseAccessor(options =>
{
    builder.Services.AddDbPool<KrsDbContext>((serviceProvider, options) =>
    {
        DbContextOptionsBuilder dbOptionBuilder = options.UseNpgsql(dbConn, optionBuilder =>
        {
            optionBuilder.MigrationsHistoryTable("__EFMigrationHistory_Krs", "public");
            optionBuilder.MigrationsAssembly("SmartLibrary.Search.Database.Migrations");
        });
    });
});


// 添加自定义任务

builder.Host.ConfigureContainer<ContainerBuilder>(containerBuilder =>
{
    containerBuilder.AddServicePart();
});

builder.Services.AddEsSearchProxy(x =>
{
    Task<string> OrOwnerHandleAsync(IServiceProvider serviceProvider)
    {
        return Task.FromResult(string.Empty);
    }
    x.SiteId = int.Parse(builder.Configuration.GetValue<string>("EsProxyConfig:SiteId") ?? "993");
    x.SitePassword = builder.Configuration.GetValue<string>("EsProxyConfig:SitePassword");
    x.SiteUserName = builder.Configuration.GetValue<string>("EsProxyConfig:SiteUserName");
#if DEBUG
    x.EsApiBase = new Uri("https://essmartapi.vipslib.com");
#else
                x.EsApiBase = new Uri(builder.Configuration.GetValue<string>("EsProxyConfig:BaseAddress"));
#endif
    x.ShowRequestLogger = true;//SiteGlobalConfig.EsProxy.ShowRequestLogger;
    x.ImageServerOption = new ImageServerOptions
    {
        SecureAddress = builder.Configuration.GetValue<string>("EsProxyConfig:ImageSecureAddress") ?? ImageServerOptions.Default.SecureAddress,
        BaseAddress = builder.Configuration.GetValue<string>("EsProxyConfig:ImageAddress") ?? ImageServerOptions.Default.BaseAddress
    };
    x.ConnectionTimeOut = TimeSpan.FromSeconds(45);
    x.ExternalHandlers = new[]
    {
        ExternalHandlers.FromAsyncInvokeFunc(EsExternalHandlerNames.OwnerHandlerName,OrOwnerHandleAsync)
        ,ExternalHandlers.FromAsyncInvokeFunc(EsExternalHandlerNames.OrOwnerHandlerName,OrOwnerHandleAsync)
    };
});

// 注册 RabbitMQ 消费者
// 注意：这里只是示例，实际使用时可以根据需要注册不同的消费者
if (builder.Configuration.GetValue<bool>("RabbitMQ:Enabled", true))
{
    // 注册示例消费者
    builder.Services.AddSingleton<IConsumer, ExampleConsumer>();

    // 可以注册更多消费者
    // builder.Services.AddSingleton<IConsumer, AnotherConsumer>();

    // 注册 RabbitMQ 消费者托管服务
    builder.Services.AddHostedService<RabbitMQConsumerHostedService>();
}

var app = builder.Build();

// 配置 Hangfire 仪表板
if (builder.Configuration.GetValue<bool>("Hangfire:Dashboard:Enabled", true))
{
    var dashboardPath = builder.Configuration.GetValue<string>("Hangfire:Dashboard:Path", "/hangfire") ?? "/hangfire";
    var dashboardUsername = builder.Configuration.GetValue<string>("Hangfire:Dashboard:Username", "admin") ?? "admin";
    var dashboardPassword = builder.Configuration.GetValue<string>("Hangfire:Dashboard:Password", "admin123") ?? "admin123";

    app.UseHangfireDashboard(dashboardPath, new DashboardOptions
    {
        Authorization = new[]
        {
            new HangfireBasicAuthenticationFilter(dashboardUsername, dashboardPassword)
        }
    });
}

// 注册定时任务
RegisterRecurringJobs();

await app.RunAsync();

void RegisterRecurringJobs()
{
    //// 注册删除过期文章任务，每天凌晨3点执行
    //RecurringJob.AddOrUpdate<DeleteExpiredArticleJob>(
    //    "delete-expired-article-job",
    //    job => job.Execute(),
    //    Cron.Daily(3));

    //// 注册删除用户日志同步任务，每天凌晨2点执行
    //RecurringJob.AddOrUpdate<DeleteUserlogsSyncJob>(
    //    "delete-userlogs-sync-job",
    //    job => job.Execute(),
    //    Cron.Daily(2));

    //// 注册文献传递投诉处理任务，每小时执行一次
    //RecurringJob.AddOrUpdate<DocumentDeliveryComplaintSettlingJob>(
    //    "document-delivery-complaint-settling-job",
    //    job => job.Execute(),
    //    Cron.Hourly);

    //// 注册文献传递创建订单处理任务，每30分钟执行一次
    //RecurringJob.AddOrUpdate<DocumentDeliveryCreateOrderHandleJob>(
    //    "document-delivery-create-order-handle-job",
    //    job => job.Execute(),
    //    "*/30 * * * *");

    //// 注册文献传递配送任务，每小时执行一次
    //RecurringJob.AddOrUpdate<DocumentDeliveryDeliveryJob>(
    //    "document-delivery-delivery-job",
    //    job => job.Execute(),
    //    Cron.Hourly);

    // 注册文献传递调度任务，每15分钟执行一次
    RecurringJob.AddOrUpdate<IDocumentDeliverySyncJob>(
        "document-delivery-dispatch-job",
        job => job.Execute(),
        "*/15 * * * *");

    //// 注册文献传递处理中任务，每小时执行一次
    //RecurringJob.AddOrUpdate<DocumentDeliveryHandleProcessingJob>(
    //    "document-delivery-handle-processing-job",
    //    job => job.Execute(),
    //    Cron.Hourly);

    //// 注册文献传递订单处理任务，每30分钟执行一次
    //RecurringJob.AddOrUpdate<DocumentDeliveryOrderHandleJob>(
    //    "document-delivery-order-handle-job",
    //    job => job.Execute(),
    //    "*/30 * * * *");

    //// 注册文献传递同步任务，每天凌晨1点执行
    //RecurringJob.AddOrUpdate<DocumentDeliverySyncJob>(
    //    "document-delivery-sync-job",
    //    job => job.Execute(),
    //    Cron.Daily(1));

    //// 注册文献付费同步任务，每天凌晨2点30分执行
    //RecurringJob.AddOrUpdate<DocumentPaidSyncJob>(
    //    "document-paid-sync-job",
    //    job => job.Execute(),
    //    "30 2 * * *");


    //// 注册到期通知同步任务，每天凌晨5点执行
    //RecurringJob.AddOrUpdate<NoticeOfExpirationSyncJob>(
    //    "notice-of-expiration-sync-job",
    //    job => job.Execute(),
    //    Cron.Daily(5));
}
