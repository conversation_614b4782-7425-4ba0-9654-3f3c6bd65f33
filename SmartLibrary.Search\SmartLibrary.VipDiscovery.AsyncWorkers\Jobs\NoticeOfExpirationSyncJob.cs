﻿using Microsoft.Extensions.Logging;
using SmartLibrary.VipDiscovery.Service.Interface;
using System;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.AsyncWorkers.Jobs
{
    /// <summary>
    /// 到期通知同步任务
    /// </summary>
    public class NoticeOfExpirationSyncJob
    {
        private readonly ILogger<NoticeOfExpirationSyncJob> _logger;
        private readonly IApplyLogService _applyLogService;
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public NoticeOfExpirationSyncJob(ILogger<NoticeOfExpirationSyncJob> logger,IApplyLogService applyLogService)
        {
            _applyLogService = applyLogService;
            _logger = logger;
        }

        /// <summary>
        /// 执行到期通知同步任务
        /// </summary>
        public async Task Execute()
        {
            try
            {
                _logger.LogInformation("开始执行到期通知同步任务");
                 _applyLogService.NoticeOfExpiration();
                _logger.LogInformation("到期通知同步任务执行完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行到期通知同步任务时发生错误");
                throw;
            }
        }
    }
}
