﻿using Microsoft.Extensions.Logging;
using SmartLibrary.VipDiscovery.Service.Interface;
using System;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.AsyncWorkers.Jobs
{
    /// <summary>
    /// 文献付费同步任务
    /// </summary>
    public class DocumentPaidSyncJob
    {
        private readonly ILogger<DocumentPaidSyncJob> _logger;
        private readonly IWebSitePaidSyncService _webSitePaidSyncService;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public DocumentPaidSyncJob(ILogger<DocumentPaidSyncJob> logger,IWebSitePaidSyncService webSitePaidSyncService)
        {
            _webSitePaidSyncService = webSitePaidSyncService;
            _logger = logger;
        }

        /// <summary>
        /// 执行文献付费同步任务
        /// </summary>
        public async Task Execute()
        {
            try
            {
                _logger.LogInformation("开始执行文献付费同步任务");
                await _webSitePaidSyncService.SyncPaidStatusAsync();
                _logger.LogInformation("文献付费同步任务执行完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行文献付费同步任务时发生错误");
                throw;
            }
        }
    }
}
