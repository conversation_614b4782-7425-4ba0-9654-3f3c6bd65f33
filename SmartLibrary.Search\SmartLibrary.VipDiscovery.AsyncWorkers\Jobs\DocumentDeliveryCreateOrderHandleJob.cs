using Microsoft.Extensions.Logging;
using SmartLibrary.VipDiscovery.AsyncWorkers.Interface;
using SmartLibrary.VipDiscovery.Service.Interface;
using System;
using System.Threading.Tasks;

namespace SmartLibrary.VipDiscovery.AsyncWorkers.Jobs
{
    /// <summary>
    /// 文献传递创建订单处理任务
    /// </summary>
    public class DocumentDeliveryCreateOrderHandleJob : IDocumentDeliveryCreateOrderHandleJob
    {
        private readonly ILogger<DocumentDeliveryCreateOrderHandleJob> _logger;
        private readonly IDocumentDeliveryService _documentDeliveryService;
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="logger">日志记录器</param>
        public DocumentDeliveryCreateOrderHandleJob( ILogger<DocumentDeliveryCreateOrderHandleJob> logger, IDocumentDeliveryService documentDeliveryService)
        {
            _logger = logger;
            _documentDeliveryService = documentDeliveryService;
        }

        /// <summary>
        /// 执行文献传递创建订单处理任务
        /// </summary>
        public async Task Execute()
        {
            try
            {
                _logger.LogInformation("开始执行文献传递创建订单处理任务");

                _documentDeliveryService.HandleDeliveryCreateOrder();
                _logger.LogInformation("文献传递创建订单处理任务执行完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "执行文献传递创建订单处理任务时发生错误");
                throw;
            }
        }
    }
}
